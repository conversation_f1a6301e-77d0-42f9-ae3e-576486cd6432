import { Injectable, Inject } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map, timeout } from 'rxjs/operators';
import { DatabricksConfig, DATABRICKS_CONFIG_TOKEN } from '../config/databricks.config';

export interface DatabricksFileInfo {
  path: string;
  name: string;
  size: number;
  modified_at: number;
  is_directory: boolean;
}

export interface DatabricksListResponse {
  files: DatabricksFileInfo[];
}

@Injectable({
  providedIn: 'root'
})
export class DatabricksService {
  private readonly apiVersion = '2.0';

  constructor(
    private http: HttpClient,
    @Inject(DATABRICKS_CONFIG_TOKEN) private config: DatabricksConfig
  ) {}

  /**
   * Gets the authorization headers for Databricks API calls
   */
  private getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Authorization': `Bearer ${this.config.token}`,
      'Content-Type': 'application/json'
    });
  }

  /**
   * Lists files in a Databricks volume directory
   * @param path The path within the volume to list files from
   * @returns Observable of file list
   */
  listFiles(path: string = ''): Observable<DatabricksFileInfo[]> {
    const fullPath = `${this.config.volumePath}/${path}`.replace(/\/+/g, '/');
    const url = `${this.config.baseUrl}/api/${this.apiVersion}/fs/list`;
    
    const body = {
      path: fullPath
    };

    return this.http.post<DatabricksListResponse>(url, body, { 
      headers: this.getHeaders() 
    }).pipe(
      timeout(this.config.timeout || 30000),
      map(response => response.files || []),
      catchError(this.handleError)
    );
  }

  /**
   * Downloads a PDF file from Databricks volume
   * @param fileName The name of the PDF file to download
   * @param subPath Optional subdirectory path within the volume
   * @returns Observable of the file as ArrayBuffer
   */
  downloadPdf(fileName: string, subPath: string = ''): Observable<ArrayBuffer> {
    const filePath = subPath 
      ? `${this.config.volumePath}/${subPath}/${fileName}`.replace(/\/+/g, '/')
      : `${this.config.volumePath}/${fileName}`.replace(/\/+/g, '/');
    
    const url = `${this.config.baseUrl}/api/${this.apiVersion}/fs/download`;
    
    const body = {
      path: filePath
    };

    return this.http.post(url, body, {
      headers: this.getHeaders(),
      responseType: 'arraybuffer'
    }).pipe(
      timeout(this.config.timeout || 30000),
      catchError(this.handleError)
    );
  }

  /**
   * Checks if a PDF file exists in the Databricks volume
   * @param fileName The name of the PDF file to check
   * @param subPath Optional subdirectory path within the volume
   * @returns Observable of boolean indicating if file exists
   */
  fileExists(fileName: string, subPath: string = ''): Observable<boolean> {
    const filePath = subPath 
      ? `${this.config.volumePath}/${subPath}/${fileName}`.replace(/\/+/g, '/')
      : `${this.config.volumePath}/${fileName}`.replace(/\/+/g, '/');
    
    const url = `${this.config.baseUrl}/api/${this.apiVersion}/fs/get-status`;
    
    const body = {
      path: filePath
    };

    return this.http.post(url, body, { 
      headers: this.getHeaders() 
    }).pipe(
      timeout(this.config.timeout || 30000),
      map(() => true),
      catchError((error: HttpErrorResponse) => {
        if (error.status === 404) {
          return [false];
        }
        return this.handleError(error);
      })
    );
  }

  /**
   * Gets file information from Databricks volume
   * @param fileName The name of the file to get info for
   * @param subPath Optional subdirectory path within the volume
   * @returns Observable of file information
   */
  getFileInfo(fileName: string, subPath: string = ''): Observable<DatabricksFileInfo> {
    const filePath = subPath 
      ? `${this.config.volumePath}/${subPath}/${fileName}`.replace(/\/+/g, '/')
      : `${this.config.volumePath}/${fileName}`.replace(/\/+/g, '/');
    
    const url = `${this.config.baseUrl}/api/${this.apiVersion}/fs/get-status`;
    
    const body = {
      path: filePath
    };

    return this.http.post<DatabricksFileInfo>(url, body, { 
      headers: this.getHeaders() 
    }).pipe(
      timeout(this.config.timeout || 30000),
      catchError(this.handleError)
    );
  }

  /**
   * Handles HTTP errors from Databricks API
   * @param error The HTTP error response
   * @returns Observable error
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An error occurred while accessing Databricks';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      // Server-side error
      switch (error.status) {
        case 401:
          errorMessage = 'Unauthorized: Invalid Databricks token';
          break;
        case 403:
          errorMessage = 'Forbidden: Insufficient permissions to access Databricks volume';
          break;
        case 404:
          errorMessage = 'Not Found: File or path does not exist in Databricks volume';
          break;
        case 429:
          errorMessage = 'Rate Limited: Too many requests to Databricks API';
          break;
        case 500:
          errorMessage = 'Internal Server Error: Databricks API error';
          break;
        default:
          errorMessage = `Databricks API Error: ${error.status} - ${error.message}`;
      }
    }

    console.error('[DatabricksService]', errorMessage, error);
    return throwError(() => new Error(errorMessage));
  }

  /**
   * Tests the connection to Databricks
   * @returns Observable of boolean indicating if connection is successful
   */
  testConnection(): Observable<boolean> {
    return this.listFiles().pipe(
      map(() => true),
      catchError(() => [false])
    );
  }
}