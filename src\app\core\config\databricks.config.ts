import { environment } from '../../../environments/environment';

export interface DatabricksConfig {
  baseUrl: string;
  token: string;
  volumePath: string;
  timeout?: number;
}

export const DATABRICKS_CONFIG: DatabricksConfig = {
  baseUrl: environment.databricks.baseUrl,
  token: environment.databricks.token,
  volumePath: environment.databricks.volumePath,
  timeout: environment.databricks.timeout
};

export const DATABRICKS_CONFIG_TOKEN = 'DATABRICKS_CONFIG';